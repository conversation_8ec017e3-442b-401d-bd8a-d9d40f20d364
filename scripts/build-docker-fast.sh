#!/bin/bash

# 快速Docker构建脚本 - 使用Maven缓存卷优化构建速度
# 支持多种构建方式

set -e

echo "🚀 开始快速Docker构建..."

# 检查是否在项目根目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 错误：请在项目根目录执行此脚本"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" = "17" ] || [ "$JAVA_VERSION" = "1" ]; then
    JAVA_MAJOR=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f2)
    if [ "$JAVA_MAJOR" = "8" ]; then
        JAVA_VERSION="8"
    fi
fi

if [ "$JAVA_VERSION" = "17" ]; then
    echo "📦 检测到Java 17，使用宿主机构建..."

    # 1. 在宿主机执行Maven构建（利用本地缓存）
    echo "📦 步骤1: 在宿主机构建Maven项目..."
    mvn clean package -pl iot-api -am -DskipTests -B

    # 检查JAR文件是否生成
    if [ ! -f "iot-api/target/iot-api-"*".jar" ]; then
        echo "❌ 错误：JAR文件构建失败"
        exit 1
    fi

    echo "✅ Maven构建完成"

    # 2. 使用优化的Dockerfile构建Docker镜像
    echo "🐳 步骤2: 构建Docker镜像..."
    docker build -t tex/beta-iot-api -f ./iot-api/Dockerfile.host-build .
else
    echo "📦 检测到Java $JAVA_VERSION，使用Docker构建（带Maven缓存）..."

    # 使用Docker BuildKit和缓存卷
    echo "🐳 使用Maven缓存卷构建Docker镜像..."
    DOCKER_BUILDKIT=1 docker build --progress=plain -t tex/beta-iot-api -f ./iot-api/Dockerfile.with-cache .
fi

echo "✅ Docker镜像构建完成"

# 3. 显示镜像信息
echo "📋 镜像信息:"
docker images | grep tex/beta-iot-api

echo "🎉 构建完成！"
echo ""
echo "💡 使用方法:"
echo "   启动容器: docker run --name iot-api-test -p 8000:8000 -d tex/beta-iot-api"
echo "   查看日志: docker logs -f iot-api-test"
echo "   停止容器: docker stop iot-api-test"
echo "   删除容器: docker rm iot-api-test"
