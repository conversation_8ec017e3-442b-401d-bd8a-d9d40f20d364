pipeline {
    agent any
    
    environment {
        // Docker镜像名称
        IMAGE_NAME = 'tex/beta-iot-api'
        IMAGE_TAG = "${BUILD_NUMBER}"
        
        // 启用Docker BuildKit（支持缓存挂载）
        DOCKER_BUILDKIT = '1'
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo '📥 检出代码...'
                checkout scm
            }
        }
        
        stage('Build Docker Image') {
            steps {
                echo '🐳 构建Docker镜像（使用Maven缓存）...'
                script {
                    // 使用带缓存的Dockerfile构建
                    sh """
                        docker build \\
                            --progress=plain \\
                            -t ${IMAGE_NAME}:${IMAGE_TAG} \\
                            -t ${IMAGE_NAME}:latest \\
                            -f ./iot-api/Dockerfile.with-cache \\
                            .
                    """
                }
            }
        }
        
        stage('Test Container') {
            steps {
                echo '🧪 测试容器启动...'
                script {
                    // 启动容器进行基本测试
                    sh """
                        # 启动容器
                        docker run -d --name iot-api-test-${BUILD_NUMBER} \\
                            -p 8000:8000 \\
                            ${IMAGE_NAME}:${IMAGE_TAG}
                        
                        # 等待启动
                        sleep 30
                        
                        # 检查健康状态
                        docker ps | grep iot-api-test-${BUILD_NUMBER}
                        
                        # 查看日志
                        docker logs iot-api-test-${BUILD_NUMBER}
                    """
                }
            }
            post {
                always {
                    // 清理测试容器
                    sh """
                        docker stop iot-api-test-${BUILD_NUMBER} || true
                        docker rm iot-api-test-${BUILD_NUMBER} || true
                    """
                }
            }
        }
        
        stage('Push to Registry') {
            when {
                branch 'main'
            }
            steps {
                echo '📤 推送镜像到仓库...'
                script {
                    // 这里可以推送到Docker Registry
                    // docker.withRegistry('https://your-registry.com', 'registry-credentials') {
                    //     sh "docker push ${IMAGE_NAME}:${IMAGE_TAG}"
                    //     sh "docker push ${IMAGE_NAME}:latest"
                    // }
                    echo "镜像构建完成: ${IMAGE_NAME}:${IMAGE_TAG}"
                }
            }
        }
    }
    
    post {
        always {
            echo '🧹 清理工作空间...'
            // 清理旧的镜像（保留最近5个版本）
            sh '''
                docker images ${IMAGE_NAME} --format "table {{.Tag}}" | tail -n +2 | sort -nr | tail -n +6 | xargs -r docker rmi ${IMAGE_NAME}: || true
            '''
        }
        success {
            echo '✅ 构建成功！'
        }
        failure {
            echo '❌ 构建失败！'
        }
    }
}
